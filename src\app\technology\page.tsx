"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronDown, ChevronRight, ArrowUp } from "lucide-react"

// Metadata for SEO
export const metadata = {
  title: '製造技術 | 寧波市北倫華日金属製品有限公司',
  description: '圧鋳技術の基本原理から応用まで、専門的な技術情報を体系的にご紹介。高真空圧鋳技術、金型設計原則、表面処理技術など、製造技術の向上と品質改善にお役立てください。',
  keywords: '圧鋳技術, ダイカスト, 金型設計, 表面処理, アルミニウム合金, 製造技術, 寧波市北倫華日金属製品有限公司'
}

export default function TechnologyPage() {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  const [showBackToTop, setShowBackToTop] = useState(false)

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Back to top functionality
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Show/hide back to top button based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Page Header */}
      <div className="relative w-full h-[300px] overflow-hidden">
        <Image
          src="images/technology/t1.jpg"
          alt="製造技術"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container-taiyo">
            <h1 className="text-3xl md:text-4xl font-bold text-white">製造技術</h1>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="bg-white py-3 border-b border-gray-200">
        <div className="container-taiyo">
          <nav className="flex text-sm">
            <Link href="/" className="text-gray-500 hover:text-primary transition-colors">
              HOME
            </Link>
            <span className="mx-2 text-gray-400">&gt;</span>
            <span className="text-gray-700">製造技術</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-12 bg-white">
        <div className="container-taiyo">
          <h2 className="text-2xl font-bold text-center mb-8">技術コラム・技術資料</h2>
          <p className="text-center text-gray-700 mb-12 max-w-3xl mx-auto">
            圧鋳技術の基本原理から応用まで、専門的な技術情報を体系的にご紹介します。
            製造技術の向上と品質改善にお役立てください。
          </p>

          {/* Table of Contents */}
          <div className="bg-gray-50 p-6 rounded-lg mb-12">
            <h3 className="text-xl font-bold mb-6 text-center">目次</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <button onClick={() => scrollToSection('section-1')} className="block text-left text-taiyo-red hover:underline transition-colors">1. 高真空圧鋳技術</button>
                <button onClick={() => scrollToSection('section-2')} className="block text-left text-taiyo-red hover:underline transition-colors">2. アルミ圧鋳金型の設計原則</button>
                <button onClick={() => scrollToSection('section-3')} className="block text-left text-taiyo-red hover:underline transition-colors">3. 圧鋳成型技術の基本原理</button>
                <button onClick={() => scrollToSection('section-4')} className="block text-left text-taiyo-red hover:underline transition-colors">4. 圧鋳工芸基本流程</button>
                <button onClick={() => scrollToSection('section-5')} className="block text-left text-taiyo-red hover:underline transition-colors">5. 圧鋳常用金属材料</button>
                <button onClick={() => scrollToSection('section-6')} className="block text-left text-taiyo-red hover:underline transition-colors">6. 圧鋳技術難点</button>
              </div>
              <div className="space-y-2">
                <button onClick={() => scrollToSection('section-7')} className="block text-left text-taiyo-red hover:underline transition-colors">7. 圧鋳技術中の常見問題</button>
                <button onClick={() => scrollToSection('section-8')} className="block text-left text-taiyo-red hover:underline transition-colors">8. 圧鋳金型の寿命</button>
                <button onClick={() => scrollToSection('section-9')} className="block text-left text-taiyo-red hover:underline transition-colors">9. 常用表面処理類型</button>
                <button onClick={() => scrollToSection('section-10')} className="block text-left text-taiyo-red hover:underline transition-colors">10. 一体化圧鋳技術について</button>
                <button onClick={() => scrollToSection('section-11')} className="block text-left text-taiyo-red hover:underline transition-colors">11. 表面処理の優位性</button>
              </div>
            </div>
          </div>

          {/* Section 1: High Vacuum Die Casting Technology */}
          <div id="section-1" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">1</span>
                高真空圧鋳技術
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                高真空圧鋳技術は、鋳物内部の気孔率を効果的に低減し、鋳物内部組織を緻密にし、その機械的性能を向上させ、充填条件を改善し、製品の引張強度、降伏強度、延伸率などを向上させることができます。金型保護の面では、型腔内の反圧力を最大限に減少させ、射出比圧を低下させ、圧鋳金型の使用寿命を延長するのに役立ち、圧鋳部品の使用範囲を拡大する重要な圧鋳技術です。
              </p>
            </div>
          </div>

          {/* Section 2: Aluminum Die Casting Mold Design Principles */}
          <div id="section-2" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">2</span>
                アルミ圧鋳金型の設計原則
              </h2>
            </div>

            {/* Section 2.1 */}
            <div className="mb-8">
              <button
                onClick={() => toggleSection('section-2-1')}
                className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
              >
                <h3 className="text-lg font-semibold text-left">2.1 製品品質要求を満たす</h3>
                {expandedSections['section-2-1'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              </button>

              {expandedSections['section-2-1'] && (
                <div className="mt-4 space-y-6">
                  {/* Section 2.1.1 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-taiyo-red">2.1.1 分型面選択原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>鋳物品質の保証：</strong>分型面は鋳物の最大輪郭部に選択し、脱型に便利で鋳物が開型後に動型側に留まるようにし、順調な取り出しを確保する必要があります。同時に、鋳物の重要な表面（配合面、外観面など）に分型面を設置することを避け、バリ、毛刺などの鋳物品質に影響する欠陥の発生を防ぐ必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>金型構造の簡素化：</strong>できるだけ平面分型面を採用し、複雑な曲面分型を避けて、金型加工の難易度とコストを低減します。曲面分型を採用する必要がある場合は、曲面の加工精度を確保し、金型合型の密封性を保証する必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>排気に有利：</strong>分型面は型腔内ガスの排出に有利であるべきで、排気溝の開設などの方式により、ガスが圧鋳過程で順調に排出され、気孔などの欠陥の発生を減少させることができます。例えば、分型面の縁部や鋳物の最後充填部位に排気溝を設置し、その深さは一般的に0.05-0.1mm、幅は鋳物の大きさによって決まり、通常5-20mmです。
                    </p>
                  </div>

                  {/* Section 2.1.2 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-taiyo-red">2.1.2 型腔と型芯設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>寸法精度：</strong>鋳物の寸法公差要求に基づき、型腔と型芯の寸法を合理的に確定します。アルミ合金の収縮率（一般的に0.8%-1.2%）、および金型使用過程での摩耗状況を考慮し、適当な加工余量と公差を予約する必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>脱型勾配：</strong>鋳物の脱型を便利にするため、型腔と型芯表面には一定の脱型勾配を設置する必要があります。脱型勾配の大きさは鋳物の壁厚、形状と合金の特性によって決まります。一般的に、アルミ合金圧鋳部品の脱型勾配は1°-3°の間です。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>強度と剛性：</strong>型腔と型芯は十分な強度と剛性を持ち、圧鋳過程での高圧と熱応力に耐える必要があります。合理的な金型材料の選択（H13鋼などの熱間工具鋼）、金型壁厚の増加または補強リブの設置などの方式により、その強度と剛性を向上させることができます。
                    </p>
                  </div>

                  {/* Section 2.1.3 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-taiyo-red">2.1.3 注湯システム設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>金属液流動の円滑性：</strong>注湯システムは金属液が迅速、均一に型腔を充填できることを保証し、渦流、飛散などの現象の発生を避け、空気の巻き込みと酸化介在物の発生を防ぐ必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>型芯への衝撃回避：</strong>注湯口の設置は金属液が直接型芯に衝撃することを避け、型芯が沖刷により摩耗や変形することを防ぎ、鋳物の寸法精度と品質に影響することを避ける必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>注湯口除去の便利性：</strong>注湯口の形状と位置は鋳物脱型後の注湯口除去に便利で、鋳物に損傷を与えないようにする必要があります。一般的に、注湯口の厚さは鋳物の壁厚より小さくし、機械加工や人工打撃の方式で除去しやすくします。
                    </p>
                  </div>

                  {/* Section 2.1.4 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-taiyo-red">2.1.4 排溢システム設計原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>効果的な排気：</strong>排溢システムは型腔内のガス、介在物と冷汚金属液を効果的に排出し、鋳物内部に気孔、冷間隔離などの欠陥が発生することを避ける必要があります。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>冷汚金属液の貯蔵：</strong>溢流槽は冷汚金属液を貯蔵する作用も果たし、冷汚金属液が鋳物の重要部位に進入し、鋳物の品質に影響することを防ぎます。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>清理の便利性：</strong>溢流槽と排気槽の設計は鋳物脱型後にその中の金属滓と雑物を清理するのに便利であるべきで、除去しやすい構造形式を採用できます。
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Section 2.2 */}
            <div className="mb-8">
              <button
                onClick={() => toggleSection('section-2-2')}
                className="w-full bg-gray-100 hover:bg-gray-200 p-4 rounded-lg flex items-center justify-between transition-colors"
              >
                <h3 className="text-lg font-semibold text-left">2.2 金型製造可能性の確保</h3>
                {expandedSections['section-2-2'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              </button>

              {expandedSections['section-2-2'] && (
                <div className="mt-4 space-y-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="text-md font-semibold mb-3 text-taiyo-red">2.2.1 金型構造簡素化原則</h4>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>部品数量の削減：</strong>金型機能を満たす前提で、できるだけ金型構造を簡素化し、金型部品の数量を削減します。これにより金型の加工難易度とコストを低減し、金型の組立精度と信頼性を向上させることができます。
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-4">
                      <strong>複雑な加工工芸の回避：</strong>金型設計時はできるだけ常規の加工工芸を採用し、フライス削、旋削、穴あけなどを使用し、過度に複雑な特殊加工工芸の採用を避けます。
                    </p>
                    <p className="text-gray-700 leading-relaxed">
                      <strong>標準化設計：</strong>できるだけ標準部品と通用部品を採用し、金型フレーム、押出ピン、ガイド柱、ガイドスリーブなどを使用します。標準化設計は金型の製造効率を向上させ、コストを低減するだけでなく、金型の保守と部品交換にも便利です。
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Section 3: Basic Principles of Die Casting Technology */}
          <div id="section-3" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">3</span>
                圧鋳成型技術の基本原理
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                圧鋳成型技術は、溶融金属材料を高圧で金型型腔に高速注入し、冷却凝固により部品を形成する製造工芸です。その基本原理には、溶融金属の高速充填、精密な金型設計と製造、合理的な冷却システム、および脱型後の後処理技術などの核心的な環節が含まれます。重力鋳造過程において、高圧の増大に伴い、溶融金属は高速条件下で型腔を完全に充満し、気孔や収縮孔などの鋳造欠陥を著しく減少させ、製品の密度と機械性能を向上させることができます。
              </p>
            </div>
          </div>

          {/* Section 4: Basic Process Flow */}
          <div id="section-4" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">4</span>
                圧鋳工芸基本流程
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                圧鋳工芸の基本流程には、材料準備、金型予熱、溶融注入、冷却凝固、脱型、後処理などの段階が含まれます。各段階は製品の最終品質に重要な影響を与えるため、厳格な工芸パラメータ制御が必要です。
              </p>
            </div>
          </div>

          {/* Section 5: Common Metal Materials */}
          <div id="section-5" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">5</span>
                圧鋳常用金属材料
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-taiyo-red">アルミニウム合金</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• ADC12、A380（軽量化、耐腐食、自動車部品に広く応用）</li>
                    <li>• A360、ADC6（高流動性+耐腐食性）</li>
                    <li>• ADC10</li>
                    <li>• Al-Si-Cu系合金（AlSi12Cu1Fe）</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-taiyo-red">亜鉛合金</h4>
                  <ul className="space-y-2 text-gray-700">
                    <li>• Zamak2、Zamak3、Zamak5</li>
                    <li>• 融点が低く、成型性が良好</li>
                    <li>• 小型精密部品に適用</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Section 6: Technical Difficulties */}
          <div id="section-6" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">6</span>
                圧鋳技術難点
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="font-semibold mb-4 text-taiyo-red">核心難点：</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 text-gray-700">
                  <p>① 金属液流動と充填制御</p>
                  <p>② 金型熱管理と寿命制御</p>
                </div>
                <div className="space-y-2 text-gray-700">
                  <p>③ 気孔と収縮欠陥制御</p>
                  <p>④ 薄壁部品と複雑構造成型</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 7: Common Problems */}
          <div id="section-7" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">7</span>
                圧鋳技術中の常見問題
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-gray-700">
                <div className="space-y-2">
                  <p>① 気孔</p>
                  <p>② 収縮孔/収縮</p>
                </div>
                <div className="space-y-2">
                  <p>③ 亀裂</p>
                  <p>④ 冷間隔離</p>
                </div>
                <div className="space-y-2">
                  <p>⑤ 欠鋳</p>
                  <p>⑥ 流痕</p>
                </div>
                <div className="space-y-2">
                  <p>⑦ 凹陥</p>
                  <p>⑧ 網状毛刺</p>
                </div>
                <div className="space-y-2">
                  <p>⑨ 色斑</p>
                  <p>⑩ 麻面</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 8: Mold Lifespan */}
          <div id="section-8" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">8</span>
                圧鋳金型の寿命
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 leading-relaxed">
                圧鋳金型の寿命は多種の要因の影響を受け、通常「万回」を単位として計算され、異なる材料と工芸条件下での寿命差は顕著です。金型の寿命は材料選択、設計品質、使用条件、保守管理などの要因と密接に関係しています。
              </p>
            </div>
          </div>

          {/* Section 9: Surface Treatment Types */}
          <div id="section-9" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">9</span>
                常用表面処理類型
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-gray-700">
                <div className="space-y-2">
                  <p>① 硬質酸化</p>
                  <p>② 塗装</p>
                </div>
                <div className="space-y-2">
                  <p>③ 電泳</p>
                  <p>④ サンドブラスト</p>
                </div>
                <div className="space-y-2">
                  <p>⑤ メッキ</p>
                  <p>⑥ 振動研磨</p>
                </div>
                <div className="space-y-2">
                  <p>⑦ 粉体塗装</p>
                  <p>⑧ フッ素コーティング</p>
                </div>
              </div>
            </div>
          </div>

          {/* Section 10: Integrated Die Casting Technology */}
          <div id="section-10" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">10</span>
                一体化圧鋳技術について
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="font-semibold mb-4 text-taiyo-red">一体化圧鋳技術の特点と優位性：</h4>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  <strong>製造効率の顕著な向上：</strong>該技術は多工程を単一工程に統合し、生産周期を大幅に短縮し、労働コストを低減します。高精度金型及び制御システムの応用により、製品の一致性と外観品質が向上します。
                </p>
                <p>
                  <strong>製品性能の全面的向上：</strong>一体化圧鋳技術により製品はより優れた機械性能と耐腐食性能を持ち、軽量化設計を実現し、現代工業の高性能と高信頼性の要求を満たします。
                </p>
                <p>
                  <strong>環境保護型脱型剤及び噴塗技術の積極的応用：</strong>環境に配慮した製造プロセスを実現し、持続可能な発展に貢献します。
                </p>
              </div>
            </div>
          </div>

          {/* Section 11: Surface Treatment Advantages */}
          <div id="section-11" className="mb-12">
            <div className="bg-taiyo-red text-white p-4 md:p-6 mb-6">
              <h2 className="text-xl font-bold flex items-center">
                <span className="bg-white text-taiyo-red h-8 w-8 flex items-center justify-center rounded-full mr-3">11</span>
                表面処理の優位性
              </h2>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 text-taiyo-red">欠陥の隠蔽</h4>
                  <p className="text-gray-700 mb-4">気孔、収縮孔修補、流痕などの圧鋳原生欠陥を効果的に隠蔽します。</p>

                  <h4 className="font-semibold mb-3 text-taiyo-red">機能性向上</h4>
                  <p className="text-gray-700">耐腐食性の飛躍的向上、耐摩耗性の向上、導熱/電気特性の最適化を実現します。</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-taiyo-red">美観と価値ブランド</h4>
                  <p className="text-gray-700 mb-4">色彩、標識などの美観性を向上させ、製品の付加価値を高めます。</p>

                  <h4 className="font-semibold mb-3 text-taiyo-red">工芸協同効果</h4>
                  <p className="text-gray-700">前処理の重要な作用、複合工芸組合によるコストと環境保護のバランスを実現します。</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          <div className="bg-taiyo-red text-white p-6 md:p-8 rounded-md">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4">技術相談・お問い合わせ</h3>
              <p className="mb-6">
                圧鋳技術に関するご質問やご相談がございましたら、お気軽にお問い合わせください。
                専門技術者が詳しくご説明いたします。
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center bg-white text-taiyo-red px-6 py-3 rounded-sm hover:bg-gray-100 transition-colors font-medium"
              >
                お問い合わせはこちら
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2"><path d="m9 18 6-6-6-6"></path></svg>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 bg-taiyo-red text-white p-3 rounded-full shadow-lg hover:bg-taiyo-red/90 transition-all duration-300 z-50"
          aria-label="ページトップに戻る"
        >
          <ArrowUp size={20} />
        </button>
      )}
    </div>
  )
}
